// Author: zhang<PERSON>yu03
// Date: 2025-02-14 17:53:19
// Description: 语音组件（包含长按按钮和语音蒙层）

import {View} from '@tarojs/components';
import React, {memo, useCallback, useRef, useState} from 'react';
import cx from 'classnames';
import {throttle} from 'lodash-es';
import {eventCenter} from '@tarojs/taro';

import useTouchEvent from '../../../hooks/common/useTouchEvent';

import VoiceModal from './voiceModal/index';
import styles from './index.module.less';

import type {VoiceComProps, VoiceModalRef} from './index.d';

const ImTriageVoice = memo((props: VoiceComProps) => {
    const {handleChangeIcon} = props;

    const [open, setOpen] = useState(false);
    const popRef = useRef<VoiceModalRef>(null);

    const handleLongPress = useCallback(() => {
        setOpen(true);
        eventCenter.trigger('stopTTS');
    }, []);

    const handleClose = useCallback(() => {
        setOpen(false);
    }, []);

    const {info, onTouchFn} = useTouchEvent({
        onTouchEnd: (msg: unknown) => {
            popRef?.current?.handleTouchEnd?.(msg);
        },
        onTouchStart: throttle((msg: unknown) => {
            popRef?.current?.handleTouchStart?.(msg);
            console.info('onTouchStart');
        }, 30000, {leading: true, trailing: false}),
        isStopEvent: true,
        onTouchMove: throttle(() => {
            popRef?.current?.handleTouchMove?.(info);
        }, 100, {leading: true, trailing: false})
    });

    return (
        <>
            <View
                onLongTap={handleLongPress}
                onLongPress={handleLongPress}
                {...onTouchFn}
                className={cx(
                    styles.imTriageVoice,
                    'wz-flex'
                    // 'wz-mb-33'
                )}
            >
                按住开始说话
            </View>
            <VoiceModal
                ref={popRef}
                open={open}
                handleClose={handleClose}
                handleChangeIcon={handleChangeIcon}
            />
        </>
    );
});

ImTriageVoice.displayName = 'ImTriageVoice';

export default ImTriageVoice;
